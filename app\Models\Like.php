<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Like extends Model
{
    protected $fillable = [
        'likeable_type',
        'likeable_id',
        'user_id',
        'reaction_type',
    ];

    /**
     * Get the likeable model (post, comment, etc.)
     */
    public function likeable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who made this like
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get reaction counts for a specific likeable item
     */
    public static function getReactionCounts($likeableType, $likeableId)
    {
        return self::where('likeable_type', $likeableType)
            ->where('likeable_id', $likeableId)
            ->selectRaw('reaction_type, COUNT(*) as count')
            ->groupBy('reaction_type')
            ->pluck('count', 'reaction_type')
            ->toArray();
    }

    /**
     * Get user's reaction for a specific likeable item
     */
    public static function getUserReaction($likeableType, $likeableId, $userId)
    {
        return self::where('likeable_type', $likeableType)
            ->where('likeable_id', $likeableId)
            ->where('user_id', $userId)
            ->first();
    }
}
