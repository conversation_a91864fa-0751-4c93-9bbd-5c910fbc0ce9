<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['targetId', 'targetType', 'currentReaction' => null, 'position' => 'bottom']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['targetId', 'targetType', 'currentReaction' => null, 'position' => 'bottom']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $reactions = config('reactions.types');
    $defaultReaction = config('reactions.default', 'like');
    $currentReactionType = $currentReaction?->reaction_type ?? null;
?>

<div class="reaction-wrapper" style="position: relative; display: inline-block;">
    <!-- Main Reaction Button -->
    <button
        class="reaction-btn flex items-center space-x-2 <?php echo e($currentReaction ? $reactions[$currentReactionType]['color'] : 'text-gray-500'); ?> transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
        data-target-id="<?php echo e($targetId); ?>"
        data-target-type="<?php echo e($targetType); ?>"
        data-current-reaction="<?php echo e($currentReactionType); ?>"
        onclick="window.reactionSystem?.toggleDefaultReaction(this)"
    >
        <?php if($currentReaction): ?>
            <span class="text-lg"><?php echo e($reactions[$currentReactionType]['emoji']); ?></span>
            <span class="text-sm font-medium"><?php echo e($reactions[$currentReactionType]['label']); ?></span>
        <?php else: ?>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M2 15V9a2 2 0 012-2h1.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V15"></path>
            </svg>
            <span class="text-sm font-medium">Like</span>
        <?php endif; ?>
    </button>

    <!-- Reaction Picker Popup -->
    <div
        class="reaction-popup"
        style="position: absolute; <?php echo e($position === 'top' ? 'bottom: 100%; margin-bottom: 4px;' : 'top: 100%; margin-top: 4px;'); ?> left: 0; background: white; border-radius: 25px; box-shadow: 0 10px 25px rgba(0,0,0,0.15); border: 1px solid #e5e7eb; padding: 8px 12px; display: flex; gap: 4px; opacity: 0; visibility: hidden; transform: scale(0.95); transition: all 0.2s ease; z-index: 1000; pointer-events: none;"
        data-target-id="<?php echo e($targetId); ?>"
        data-target-type="<?php echo e($targetType); ?>"
    >
        <?php $__currentLoopData = $reactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $reaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button
                class="reaction-btn-option"
                style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; border: none; background: transparent; cursor: pointer; transition: all 0.2s ease; <?php echo e($currentReactionType === $type ? 'background: #dbeafe; transform: scale(1.1); box-shadow: 0 0 0 2px #3b82f6;' : ''); ?>"
                data-reaction-type="<?php echo e($type); ?>"
                data-target-id="<?php echo e($targetId); ?>"
                data-target-type="<?php echo e($targetType); ?>"
                title="<?php echo e($reaction['label']); ?>"
                onclick="window.reactionSystem?.selectReaction(this); event.stopPropagation();"
                onmouseover="this.style.background='#f3f4f6'; this.style.transform='scale(1.25)';"
                onmouseout="this.style.background='<?php echo e($currentReactionType === $type ? '#dbeafe' : 'transparent'); ?>'; this.style.transform='<?php echo e($currentReactionType === $type ? 'scale(1.1)' : 'scale(1)'); ?>';"
            >
                <span style="font-size: 20px;"><?php echo e($reaction['emoji']); ?></span>
            </button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<style>
.reaction-wrapper:hover .reaction-popup {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
    pointer-events: auto !important;
}
</style>


<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/reaction-picker.blade.php ENDPATH**/ ?>