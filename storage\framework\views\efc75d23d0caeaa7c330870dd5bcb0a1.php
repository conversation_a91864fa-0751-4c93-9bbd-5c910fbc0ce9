<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['targetId', 'targetType' => 'post', 'currentReaction' => null, 'position' => 'bottom']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['targetId', 'targetType' => 'post', 'currentReaction' => null, 'position' => 'bottom']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $reactions = config('reactions.types');
    $defaultReaction = config('reactions.default', 'like');
    $currentReactionType = $currentReaction?->reaction_type ?? null;
?>

<div class="relative inline-block">
    <!-- Main Reaction Button -->
    <button
        class="reaction-main-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
        data-target-id="<?php echo e($targetId); ?>"
        data-target-type="<?php echo e($targetType); ?>"
        data-current-reaction="<?php echo e($currentReactionType); ?>"
    >
        <?php if($currentReaction): ?>
            <span class="text-lg"><?php echo e($reactions[$currentReactionType]['emoji']); ?></span>
            <span class="text-sm font-medium <?php echo e($reactions[$currentReactionType]['color']); ?>">
                <?php echo e($reactions[$currentReactionType]['label']); ?>

            </span>
        <?php else: ?>
            <span class="text-lg">👍</span>
            <span class="text-sm font-medium">Like</span>
        <?php endif; ?>
    </button>

    <!-- Reaction Picker Popup -->
    <div
        class="reaction-picker absolute <?php echo e($position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'); ?> left-1/2 transform -translate-x-1/2 bg-white rounded-full shadow-lg border border-gray-200 px-3 py-2 flex space-x-2 opacity-0 invisible transition-all duration-200 scale-95 z-50"
        data-target-id="<?php echo e($targetId); ?>"
        data-target-type="<?php echo e($targetType); ?>"
    >
        <?php $__currentLoopData = $reactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $reaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button
                class="reaction-option flex items-center justify-center w-12 h-12 rounded-full <?php echo e($reaction['bg_color']); ?> hover:scale-125 transition-all duration-200 transform <?php echo e($currentReactionType === $type ? 'ring-2 ring-blue-500 scale-110' : ''); ?>"
                data-reaction-type="<?php echo e($type); ?>"
                data-target-id="<?php echo e($targetId); ?>"
                data-target-type="<?php echo e($targetType); ?>"
                title="<?php echo e($reaction['label']); ?>"
            >
                <span class="text-2xl"><?php echo e($reaction['emoji']); ?></span>
            </button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<style>
@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-8px); }
    70% { transform: translateY(-4px); }
    90% { transform: translateY(-2px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

.reaction-animation-heartBeat { animation: heartBeat 0.6s ease-in-out; }
.reaction-animation-bounce { animation: bounce 0.6s ease-in-out; }
.reaction-animation-shake { animation: shake 0.6s ease-in-out; }
.reaction-animation-pulse { animation: pulse 0.6s ease-in-out; }
.reaction-animation-fadeIn { animation: fadeIn 0.6s ease-in-out; }
</style>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/reaction-picker.blade.php ENDPATH**/ ?>