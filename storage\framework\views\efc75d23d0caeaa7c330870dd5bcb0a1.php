<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['targetId', 'targetType', 'currentReaction' => null, 'position' => 'bottom']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['targetId', 'targetType', 'currentReaction' => null, 'position' => 'bottom']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $reactions = config('reactions.types');
    $defaultReaction = config('reactions.default', 'like');
    $currentReactionType = $currentReaction?->reaction_type ?? null;
?>

<div class="relative inline-block">
    <!-- Main Reaction Button -->
    <button
        class="reaction-main-btn flex items-center space-x-2 <?php echo e($currentReaction ? $reactions[$currentReactionType]['color'] : 'text-gray-500'); ?> transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
        data-target-id="<?php echo e($targetId); ?>"
        data-target-type="<?php echo e($targetType); ?>"
        data-current-reaction="<?php echo e($currentReactionType); ?>"
        aria-label="<?php echo e($currentReaction ? 'Change reaction' : 'Add reaction'); ?>"
        role="button"
        tabindex="0"
    >
        <?php if($currentReaction): ?>
            <span class="text-lg" aria-hidden="true"><?php echo e($reactions[$currentReactionType]['emoji']); ?></span>
            <span class="text-sm font-medium">
                <?php echo e($reactions[$currentReactionType]['label']); ?>

            </span>
        <?php else: ?>
            <!-- Thumbs up SVG icon instead of emoji for better styling control -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M2 15V9a2 2 0 012-2h1.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V15"></path>
            </svg>
            <span class="text-sm font-medium">Like</span>
        <?php endif; ?>
    </button>

    <!-- Reaction Picker Popup -->
    <div
        class="reaction-picker absolute <?php echo e($position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'); ?> left-0 bg-white rounded-full shadow-xl border border-gray-200 px-3 py-2 flex space-x-1 opacity-0 invisible transition-all duration-200 scale-95 z-50"
        data-target-id="<?php echo e($targetId); ?>"
        data-target-type="<?php echo e($targetType); ?>"
        role="toolbar"
        aria-label="Choose reaction"
    >
        <?php $__currentLoopData = $reactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $reaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <button
                class="reaction-option flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 hover:scale-125 transition-all duration-200 transform <?php echo e($currentReactionType === $type ? 'ring-2 ring-blue-500 scale-110 bg-blue-50' : ''); ?>"
                data-reaction-type="<?php echo e($type); ?>"
                data-target-id="<?php echo e($targetId); ?>"
                data-target-type="<?php echo e($targetType); ?>"
                title="<?php echo e($reaction['label']); ?>"
                aria-label="<?php echo e($reaction['label']); ?>"
                role="button"
                tabindex="-1"
                onclick="event.stopPropagation();"
            >
                <span class="text-xl" aria-hidden="true"><?php echo e($reaction['emoji']); ?></span>
            </button>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<style>
/* Reaction animations */
@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-30px,0); }
    70% { transform: translate3d(0,-15px,0); }
    90% { transform: translate3d(0,-4px,0); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

/* Animation classes */
.reaction-animation-heartBeat { animation: heartBeat 0.6s ease-in-out; }
.reaction-animation-bounce { animation: bounce 0.6s ease-in-out; }
.reaction-animation-shake { animation: shake 0.6s ease-in-out; }
.reaction-animation-pulse { animation: pulse 0.6s ease-in-out; }
.reaction-animation-fadeIn { animation: fadeIn 0.6s ease-in-out; }

/* Mobile touch feedback */
@media (hover: none) and (pointer: coarse) {
    .reaction-option:active {
        transform: scale(1.1);
        background-color: rgba(0, 0, 0, 0.1);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .reaction-picker {
        border: 2px solid #000;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    }
    
    .reaction-option {
        border: 1px solid #666;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .reaction-option,
    .reaction-picker,
    .reaction-main-btn {
        transition: none;
    }
    
    .reaction-animation-heartBeat,
    .reaction-animation-bounce,
    .reaction-animation-shake,
    .reaction-animation-pulse,
    .reaction-animation-fadeIn {
        animation: none;
    }
}

/* Focus styles for accessibility */
.reaction-main-btn:focus,
.reaction-option:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Hover states - show picker on hover */
.reaction-main-btn:hover + .reaction-picker,
.reaction-picker:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
}

/* Ensure picker stays visible when hovering over it */
.reaction-picker {
    pointer-events: auto;
}

/* Hide picker by default */
.reaction-picker {
    pointer-events: none;
}

.reaction-main-btn:hover + .reaction-picker,
.reaction-picker:hover {
    pointer-events: auto;
}
</style>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/reaction-picker.blade.php ENDPATH**/ ?>