<?php

namespace App\Http\Controllers;

use App\Models\Share;
use App\Models\Comment;
use Illuminate\Http\Request;

class ShareCommentController extends Controller
{
    /**
     * Store a newly created comment on a share.
     */
    public function store(Request $request, Share $share)
    {
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['commentable_type'] = Share::class;
        $validated['commentable_id'] = $share->id;

        $comment = Comment::create($validated);
        $comment->load('user', 'likes');

        // Add like status for current user
        $comment->is_liked_by_user = $comment->isLikedBy(auth()->user());
        $comment->likes_count = $comment->likes()->count();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'comments_count' => $share->comments()->count()
            ]);
        }

        return back()->with('success', 'Comment added successfully!');
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment)
    {
        // Check if user can edit this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $validated = $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        $comment->update($validated);
        $comment->load('user', 'likes');

        return response()->json([
            'success' => true,
            'comment' => $comment
        ]);
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment)
    {
        // Check if user can delete this comment
        if ($comment->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $shareId = $comment->commentable_id;
        $comment->delete();

        $share = Share::find($shareId);

        return response()->json([
            'success' => true,
            'message' => 'Comment deleted successfully!',
            'comments_count' => $share ? $share->comments()->count() : 0
        ]);
    }

    /**
     * Toggle like on a comment (legacy support)
     */
    public function toggleLike(Comment $comment)
    {
        return $this->react($comment, request()->merge(['reaction_type' => 'like']));
    }

    /**
     * Handle reaction on a comment
     */
    public function react(Comment $comment, Request $request = null)
    {
        $request = $request ?: request();
        $user = auth()->user();
        $reactionType = $request->input('reaction_type');

        // Validate reaction type
        $validReactions = array_keys(config('reactions.types', []));
        if ($reactionType && !in_array($reactionType, $validReactions)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid reaction type'
            ], 400);
        }

        // Find existing reaction
        $existingReaction = $comment->likes()->where('user_id', $user->id)->first();

        if ($existingReaction) {
            if ($reactionType && $existingReaction->reaction_type === $reactionType) {
                // Same reaction - toggle off
                $existingReaction->delete();
                $userReaction = null;
            } elseif ($reactionType) {
                // Different reaction - update
                $existingReaction->update(['reaction_type' => $reactionType]);
                $userReaction = $existingReaction->fresh();
            } else {
                // No reaction type provided - remove reaction
                $existingReaction->delete();
                $userReaction = null;
            }
        } else {
            if ($reactionType) {
                // Create new reaction
                $userReaction = $comment->likes()->create([
                    'user_id' => $user->id,
                    'reaction_type' => $reactionType
                ]);
            } else {
                $userReaction = null;
            }
        }

        return response()->json([
            'success' => true,
            'user_reaction' => $userReaction,
            'reaction_counts' => $comment->getReactionCounts(),
            'total_reactions' => $comment->getTotalReactionsCount(),
            // Legacy support
            'liked' => $userReaction && $userReaction->reaction_type === 'like',
            'likes_count' => $comment->likes()->count()
        ]);
    }
}
