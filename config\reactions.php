<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Reaction
    |--------------------------------------------------------------------------
    |
    | This value determines the default reaction type when users click
    | the main reaction button without selecting a specific reaction.
    |
    */
    'default' => 'like',

    /*
    |--------------------------------------------------------------------------
    | Reaction Types
    |--------------------------------------------------------------------------
    |
    | This array defines all available reaction types with their properties
    | including emoji, label, color classes, and background colors.
    |
    */
    'types' => [
        'like' => [
            'emoji' => '👍',
            'label' => 'Like',
            'color' => 'text-blue-600',
            'bg_color' => 'hover:bg-blue-50',
            'animation' => 'bounce',
        ],
        'love' => [
            'emoji' => '❤️',
            'label' => 'Love',
            'color' => 'text-red-600',
            'bg_color' => 'hover:bg-red-50',
            'animation' => 'heartBeat',
        ],
        'haha' => [
            'emoji' => '😂',
            'label' => 'Haha',
            'color' => 'text-yellow-600',
            'bg_color' => 'hover:bg-yellow-50',
            'animation' => 'shake',
        ],
        'wow' => [
            'emoji' => '😮',
            'label' => 'Wow',
            'color' => 'text-purple-600',
            'bg_color' => 'hover:bg-purple-50',
            'animation' => 'pulse',
        ],
        'sad' => [
            'emoji' => '😢',
            'label' => 'Sad',
            'color' => 'text-gray-600',
            'bg_color' => 'hover:bg-gray-50',
            'animation' => 'fadeIn',
        ],
        'angry' => [
            'emoji' => '😠',
            'label' => 'Angry',
            'color' => 'text-orange-600',
            'bg_color' => 'hover:bg-orange-50',
            'animation' => 'shake',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Animation Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for reaction animations and timing.
    |
    */
    'animations' => [
        'duration' => '0.6s',
        'easing' => 'ease-in-out',
        'hover_scale' => '1.25',
        'selected_scale' => '1.1',
    ],

    /*
    |--------------------------------------------------------------------------
    | Mobile Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for mobile-specific behavior.
    |
    */
    'mobile' => [
        'long_press_duration' => 500, // milliseconds
        'touch_feedback' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Accessibility Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for accessibility features.
    |
    */
    'accessibility' => [
        'keyboard_navigation' => true,
        'screen_reader_labels' => true,
        'high_contrast_mode' => false,
    ],
];
