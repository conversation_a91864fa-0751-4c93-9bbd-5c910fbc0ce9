@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Color Scheme Variables */
:root {
    --color-lightest: #EEEEEE; /* Light Gray */
    --color-third-darkest: #7BC74D; /* Green */
    --color-second-darkest: #393E46; /* Dark Gray */
    --color-darkest: #222831; /* Dark Navy */
}

/* Custom Color Utility Classes */
@layer utilities {
    /* Background Colors */
    .bg-custom-lightest { background-color: var(--color-lightest); }
    .bg-custom-green { background-color: var(--color-third-darkest); }
    .bg-custom-dark-gray { background-color: var(--color-second-darkest); }
    .bg-custom-darkest { background-color: var(--color-darkest); }

    /* Text Colors */
    .text-custom-lightest { color: var(--color-lightest); }
    .text-custom-green { color: var(--color-third-darkest); }
    .text-custom-dark-gray { color: var(--color-second-darkest); }
    .text-custom-darkest { color: var(--color-darkest); }

    /* Border Colors */
    .border-custom-lightest { border-color: var(--color-lightest); }
    .border-custom-green { border-color: var(--color-third-darkest); }
    .border-custom-dark-gray { border-color: var(--color-second-darkest); }
    .border-custom-darkest { border-color: var(--color-darkest); }
}

/* Custom styles for UniLink */
@layer components {
    /* Feed layout responsive adjustments */
    .feed-container {
        @apply max-w-2xl mx-auto;
    }

    /* Smooth transitions for interactive elements */
    .transition-colors {
        transition-property: color, background-color, border-color;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
    }

    /* Custom scrollbar for sidebars */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Line clamp utilities */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* 3-Column Layout Styles */
@layer utilities {
    /* Ensure proper 3-column layout behavior */
    .three-column-layout {
        display: flex;
        height: 100vh;
        padding-top: 4rem; /* Account for fixed header */
    }

    .three-column-layout > * {
        flex-shrink: 0;
    }

    .three-column-layout .central-feed {
        flex: 1;
        min-width: 0; /* Prevents flex item from overflowing */
    }

    /* Ensure body doesn't scroll when using fixed layout */
    body.fixed-layout {
        overflow: hidden;
    }

    /* Force right sidebar to show on lg screens and up */
    @media (min-width: 1024px) {
        .force-show-right-sidebar {
            display: flex !important;
        }
    }

    /* Mobile responsive adjustments */
    @media (max-width: 1279px) {
        .xl\:mr-80 {
            margin-right: 0 !important;
        }

        .xl\:mr-96 {
            margin-right: 0 !important;
        }
    }

    /* Ensure sidebars don't interfere on smaller screens */
    @media (max-width: 1023px) {
        .lg\:ml-64 {
            margin-left: 0 !important;
        }
    }
}

/* Reaction System Styles */
@layer components {
    /* Reaction picker hover behavior */
    .reaction-main-btn:hover + .reaction-picker,
    .reaction-picker:hover {
        @apply opacity-100 visible;
        transform: translateX(-50%) scale(1);
    }

    /* Reaction option hover effects */
    .reaction-option {
        @apply transition-all duration-200 ease-in-out;
    }

    .reaction-option:hover {
        @apply scale-125;
    }

    .reaction-option:active {
        @apply scale-110;
    }

    /* Selected reaction styling */
    .reaction-option.selected {
        @apply ring-2 ring-blue-500 scale-110;
    }

    /* Mobile touch feedback */
    @media (hover: none) and (pointer: coarse) {
        .reaction-option:active {
            @apply scale-110 bg-gray-100;
        }

        .reaction-main-btn:active {
            @apply bg-gray-200;
        }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .reaction-picker {
            @apply border-2 border-black;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .reaction-option {
            @apply border border-gray-600;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .reaction-option,
        .reaction-picker,
        .reaction-main-btn {
            @apply transition-none;
        }

        .reaction-animation-heartBeat,
        .reaction-animation-bounce,
        .reaction-animation-shake,
        .reaction-animation-pulse,
        .reaction-animation-fadeIn {
            animation: none !important;
        }
    }

    /* Focus styles for accessibility */
    .reaction-main-btn:focus,
    .reaction-option:focus {
        @apply outline-2 outline-blue-500 outline-offset-2;
        outline-style: solid;
    }

    /* Reaction count styling */
    .reaction-count {
        @apply text-sm text-gray-600 font-medium;
    }

    /* Reaction picker positioning */
    .reaction-picker {
        z-index: 9999;
    }
}
