<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Share;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShareController extends Controller
{
    /**
     * Share a post to user's timeline
     */
    public function shareToTimeline(Request $request, Post $post)
    {
        $validated = $request->validate([
            'message' => 'nullable|string|max:500',
            'privacy_scope' => 'required|in:public,friends,only_me,custom',
        ]);

        // Check if user already shared this post to timeline
        $existingShare = Share::where('user_id', Auth::id())
            ->where('post_id', $post->id)
            ->where('share_type', 'direct')
            ->first();

        if ($existingShare) {
            return response()->json([
                'success' => false,
                'message' => 'You have already shared this post!'
            ], 422);
        }

        $share = Share::create([
            'user_id' => Auth::id(),
            'post_id' => $post->id,
            'message' => $validated['message'] ?? null,
            'share_type' => 'direct',
            'privacy_scope' => $validated['privacy_scope'],
        ]);

        $share->load('user', 'post');

        return response()->json([
            'success' => true,
            'message' => 'Post shared successfully!',
            'share' => $share,
            'shares_count' => $post->shares()->count()
        ]);
    }

    /**
     * Record external share (social media)
     */
    public function shareExternal(Request $request, Post $post)
    {
        $validated = $request->validate([
            'platform' => 'required|in:facebook,twitter,linkedin,copy_link',
        ]);

        // For external shares, we allow multiple shares to different platforms
        $share = Share::create([
            'user_id' => Auth::id(),
            'post_id' => $post->id,
            'share_type' => $validated['platform'],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Share recorded successfully!',
            'shares_count' => $post->shares()->count()
        ]);
    }

    /**
     * Get shares for a post
     */
    public function getShares(Post $post)
    {
        $shares = $post->shares()
            ->with('user')
            ->where('share_type', 'direct') // Only timeline shares
            ->latest()
            ->paginate(10);

        return response()->json([
            'success' => true,
            'shares' => $shares
        ]);
    }

    /**
     * Update a share (edit message)
     */
    public function update(Request $request, Share $share)
    {
        // Check if user can edit this share
        if ($share->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $validated = $request->validate([
            'message' => 'nullable|string|max:500',
            'privacy_scope' => 'sometimes|in:public,friends,only_me,custom',
        ]);

        $updateData = ['message' => $validated['message'] ?? null];

        if (isset($validated['privacy_scope'])) {
            $updateData['privacy_scope'] = $validated['privacy_scope'];
        }

        $share->update($updateData);

        $share->load('user', 'post');

        return response()->json([
            'success' => true,
            'message' => 'Share updated successfully!',
            'share' => $share
        ]);
    }

    /**
     * Remove a share
     */
    public function destroy(Share $share)
    {
        // Check if user can delete this share
        if ($share->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $postId = $share->post_id;
        $share->delete();

        $post = Post::find($postId);

        return response()->json([
            'success' => true,
            'message' => 'Share removed successfully!',
            'shares_count' => $post ? $post->shares()->count() : 0
        ]);
    }

    /**
     * Toggle like on a share (legacy support)
     */
    public function toggleLike(Share $share)
    {
        return $this->react($share, request()->merge(['reaction_type' => 'like']));
    }

    /**
     * Handle reaction on a share
     */
    public function react(Share $share, Request $request = null)
    {
        $request = $request ?: request();
        $user = Auth::user();
        $reactionType = $request->input('reaction_type');

        // Validate reaction type
        $validReactions = array_keys(config('reactions.types', []));
        if ($reactionType && !in_array($reactionType, $validReactions)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid reaction type'
            ], 400);
        }

        // Find existing reaction
        $existingReaction = $share->likes()->where('user_id', $user->id)->first();

        if ($existingReaction) {
            if ($reactionType && $existingReaction->reaction_type === $reactionType) {
                // Same reaction - toggle off
                $existingReaction->delete();
                $userReaction = null;
            } elseif ($reactionType) {
                // Different reaction - update
                $existingReaction->update(['reaction_type' => $reactionType]);
                $userReaction = $existingReaction->fresh();
            } else {
                // No reaction type provided - remove reaction
                $existingReaction->delete();
                $userReaction = null;
            }
        } else {
            if ($reactionType) {
                // Create new reaction
                $userReaction = $share->likes()->create([
                    'user_id' => $user->id,
                    'reaction_type' => $reactionType
                ]);
            } else {
                $userReaction = null;
            }
        }

        return response()->json([
            'success' => true,
            'user_reaction' => $userReaction,
            'reaction_counts' => $share->getReactionCounts(),
            'total_reactions' => $share->getTotalReactionsCount(),
            // Legacy support
            'liked' => $userReaction && $userReaction->reaction_type === 'like',
            'likes_count' => $share->likes()->count()
        ]);
    }
}
