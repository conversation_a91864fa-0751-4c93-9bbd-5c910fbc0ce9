@props(['targetId', 'targetType', 'currentReaction' => null, 'position' => 'bottom'])

@php
    $reactions = config('reactions.types');
    $defaultReaction = config('reactions.default', 'like');
    $currentReactionType = $currentReaction?->reaction_type ?? null;
@endphp

<div class="relative inline-block">
    <!-- Main Reaction Button -->
    <button
        class="reaction-main-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
        data-target-id="{{ $targetId }}"
        data-target-type="{{ $targetType }}"
        data-current-reaction="{{ $currentReactionType }}"
        aria-label="{{ $currentReaction ? 'Change reaction' : 'Add reaction' }}"
        role="button"
        tabindex="0"
    >
        @if($currentReaction)
            <span class="text-lg" aria-hidden="true">{{ $reactions[$currentReactionType]['emoji'] }}</span>
            <span class="text-sm font-medium {{ $reactions[$currentReactionType]['color'] }}">
                {{ $reactions[$currentReactionType]['label'] }}
            </span>
        @else
            <span class="text-lg" aria-hidden="true">👍</span>
            <span class="text-sm font-medium">Like</span>
        @endif
    </button>

    <!-- Reaction Picker Popup -->
    <div
        class="reaction-picker absolute {{ $position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2' }} left-1/2 transform -translate-x-1/2 bg-white rounded-full shadow-lg border border-gray-200 px-3 py-2 flex space-x-2 opacity-0 invisible transition-all duration-200 scale-95 z-50"
        data-target-id="{{ $targetId }}"
        data-target-type="{{ $targetType }}"
        role="toolbar"
        aria-label="Choose reaction"
    >
        @foreach($reactions as $type => $reaction)
            <button
                class="reaction-option flex items-center justify-center w-12 h-12 rounded-full {{ $reaction['bg_color'] }} hover:scale-125 transition-all duration-200 transform {{ $currentReactionType === $type ? 'ring-2 ring-blue-500 scale-110' : '' }}"
                data-reaction-type="{{ $type }}"
                data-target-id="{{ $targetId }}"
                data-target-type="{{ $targetType }}"
                title="{{ $reaction['label'] }}"
                aria-label="{{ $reaction['label'] }}"
                role="button"
                tabindex="-1"
            >
                <span class="text-2xl" aria-hidden="true">{{ $reaction['emoji'] }}</span>
            </button>
        @endforeach
    </div>
</div>

<style>
/* Reaction animations */
@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-30px,0); }
    70% { transform: translate3d(0,-15px,0); }
    90% { transform: translate3d(0,-4px,0); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

/* Animation classes */
.reaction-animation-heartBeat { animation: heartBeat 0.6s ease-in-out; }
.reaction-animation-bounce { animation: bounce 0.6s ease-in-out; }
.reaction-animation-shake { animation: shake 0.6s ease-in-out; }
.reaction-animation-pulse { animation: pulse 0.6s ease-in-out; }
.reaction-animation-fadeIn { animation: fadeIn 0.6s ease-in-out; }

/* Mobile touch feedback */
@media (hover: none) and (pointer: coarse) {
    .reaction-option:active {
        transform: scale(1.1);
        background-color: rgba(0, 0, 0, 0.1);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .reaction-picker {
        border: 2px solid #000;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    }
    
    .reaction-option {
        border: 1px solid #666;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .reaction-option,
    .reaction-picker,
    .reaction-main-btn {
        transition: none;
    }
    
    .reaction-animation-heartBeat,
    .reaction-animation-bounce,
    .reaction-animation-shake,
    .reaction-animation-pulse,
    .reaction-animation-fadeIn {
        animation: none;
    }
}

/* Focus styles for accessibility */
.reaction-main-btn:focus,
.reaction-option:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Hover states */
.reaction-main-btn:hover .reaction-picker {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) scale(1);
}

/* Keep picker visible when hovering over it */
.reaction-picker:hover {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) scale(1);
}
</style>
