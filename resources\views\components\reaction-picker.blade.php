@props(['targetId', 'targetType', 'currentReaction' => null, 'position' => 'bottom'])

@php
    $reactions = config('reactions.types');
    $defaultReaction = config('reactions.default', 'like');
    $currentReactionType = $currentReaction?->reaction_type ?? null;
@endphp

<div class="reaction-wrapper" style="position: relative; display: inline-block;">
    <!-- Main Reaction Button -->
    <button
        class="reaction-btn flex items-center space-x-2 {{ $currentReaction ? $reactions[$currentReactionType]['color'] : 'text-gray-500' }} transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
        data-target-id="{{ $targetId }}"
        data-target-type="{{ $targetType }}"
        data-current-reaction="{{ $currentReactionType }}"
        onclick="window.reactionSystem?.toggleDefaultReaction(this)"
        onmouseenter="window.reactionSystem?.showPopup(this.parentElement.querySelector('.reaction-popup'))"
        onmouseleave="window.reactionSystem?.hidePopupWithDelay(this.parentElement.querySelector('.reaction-popup'))"
    >
        @if($currentReaction)
            <span class="text-lg">{{ $reactions[$currentReactionType]['emoji'] }}</span>
            <span class="text-sm font-medium">{{ $reactions[$currentReactionType]['label'] }}</span>
        @else
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M2 15V9a2 2 0 012-2h1.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V15"></path>
            </svg>
            <span class="text-sm font-medium">Like</span>
        @endif
    </button>

    <!-- Reaction Picker Popup -->
    <div
        class="reaction-popup"
        style="position: absolute; {{ $position === 'top' ? 'bottom: 100%; margin-bottom: 4px;' : 'top: 100%; margin-top: 4px;' }} left: 0; background: white; border-radius: 25px; box-shadow: 0 10px 25px rgba(0,0,0,0.15); border: 1px solid #e5e7eb; padding: 8px 12px; display: flex; gap: 4px; opacity: 0; visibility: hidden; transform: scale(0.95); transition: all 0.3s ease; z-index: 1000; pointer-events: none;"
        data-target-id="{{ $targetId }}"
        data-target-type="{{ $targetType }}"
        onmouseenter="window.reactionSystem?.showPopup(this)"
        onmouseleave="window.reactionSystem?.hidePopupWithDelay(this)"
    >
        @foreach($reactions as $type => $reaction)
            <button
                class="reaction-btn-option"
                style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; border: none; background: transparent; cursor: pointer; transition: all 0.2s ease; {{ $currentReactionType === $type ? 'background: #dbeafe; transform: scale(1.1); box-shadow: 0 0 0 2px #3b82f6;' : '' }}"
                data-reaction-type="{{ $type }}"
                data-target-id="{{ $targetId }}"
                data-target-type="{{ $targetType }}"
                title="{{ $reaction['label'] }}"
                onclick="window.reactionSystem?.selectReaction(this); event.stopPropagation();"
                onmouseover="this.style.background='#f3f4f6'; this.style.transform='scale(1.25)';"
                onmouseout="this.style.background='{{ $currentReactionType === $type ? '#dbeafe' : 'transparent' }}'; this.style.transform='{{ $currentReactionType === $type ? 'scale(1.1)' : 'scale(1)' }}';"
            >
                <span style="font-size: 20px;">{{ $reaction['emoji'] }}</span>
            </button>
        @endforeach
    </div>
</div>

<style>
/* Popup will be controlled by JavaScript for delayed hiding */
.reaction-popup.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
    pointer-events: auto !important;
}

.reaction-popup.hide {
    opacity: 0 !important;
    visibility: hidden !important;
    transform: scale(0.95) !important;
    pointer-events: none !important;
}
</style>


