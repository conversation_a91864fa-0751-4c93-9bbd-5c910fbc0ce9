/**
 * Facebook-style Reaction System
 * Handles hover/long-press functionality, real-time updates, toggle behavior, and visual feedback
 */

class ReactionSystem {
    constructor() {
        this.longPressTimer = null;
        this.longPressDuration = 500; // milliseconds
        this.isMobile = this.detectMobile();
        this.init();
    }

    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }

    init() {
        this.bindEvents();
        this.setupKeyboardNavigation();
    }

    bindEvents() {
        document.addEventListener('click', this.handleReactionClick.bind(this));

        if (this.isMobile) {
            document.addEventListener('touchstart', this.handleTouchStart.bind(this));
            document.addEventListener('touchend', this.handleTouchEnd.bind(this));
        } else {
            document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
            document.addEventListener('mouseleave', this.handleMouseLeave.bind(this), true);
        }

        // Add specific event listeners for reaction containers
        document.addEventListener('mouseenter', (e) => {
            if (e.target.closest('.relative') && e.target.closest('.relative').querySelector('.reaction-picker')) {
                const container = e.target.closest('.relative');
                const picker = container.querySelector('.reaction-picker');
                const button = container.querySelector('.reaction-main-btn');

                if (picker && button) {
                    this.showReactionPicker(button);
                }
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.closest('.relative') && e.target.closest('.relative').querySelector('.reaction-picker')) {
                const container = e.target.closest('.relative');
                const picker = container.querySelector('.reaction-picker');
                const button = container.querySelector('.reaction-main-btn');

                if (picker && button) {
                    setTimeout(() => {
                        // Only hide if mouse is completely outside the container
                        if (!container.matches(':hover')) {
                            this.hideReactionPicker(button);
                        }
                    }, 200);
                }
            }
        }, true);
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('reaction-main-btn')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleDefaultReaction(e.target);
                } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    this.showReactionPicker(e.target);
                }
            } else if (e.target.classList.contains('reaction-option')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.selectReaction(e.target);
                }
            }
        });
    }

    handleMouseEnter(e) {
        if (e.target.classList.contains('reaction-main-btn') || e.target.closest('.reaction-main-btn')) {
            const btn = e.target.classList.contains('reaction-main-btn') ? e.target : e.target.closest('.reaction-main-btn');
            this.showReactionPicker(btn);
        }
    }

    handleMouseLeave(e) {
        if (e.target.classList.contains('reaction-main-btn') || e.target.closest('.reaction-main-btn')) {
            const btn = e.target.classList.contains('reaction-main-btn') ? e.target : e.target.closest('.reaction-main-btn');
            setTimeout(() => {
                const container = btn.parentElement;
                const picker = container.querySelector('.reaction-picker');
                // Only hide if not hovering over button, picker, or container
                if (picker && !container.matches(':hover')) {
                    this.hideReactionPicker(btn);
                }
            }, 300); // Increased delay
        }
    }

    handleTouchStart(e) {
        if (e.target.classList.contains('reaction-main-btn')) {
            this.longPressTimer = setTimeout(() => {
                this.showReactionPicker(e.target);
                // Add haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }, this.longPressDuration);
        }
    }

    handleTouchEnd(e) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    handleReactionClick(e) {
        // Handle reaction option clicks
        if (e.target.classList.contains('reaction-option') || e.target.closest('.reaction-option')) {
            e.preventDefault();
            e.stopPropagation();
            const option = e.target.classList.contains('reaction-option') ? e.target : e.target.closest('.reaction-option');
            this.selectReaction(option);
            return;
        }

        // Handle main button clicks
        if (e.target.classList.contains('reaction-main-btn') || e.target.closest('.reaction-main-btn')) {
            const btn = e.target.classList.contains('reaction-main-btn') ?
                       e.target : e.target.closest('.reaction-main-btn');

            if (!this.isMobile) {
                e.preventDefault();
                this.toggleDefaultReaction(btn);
            }
        }
    }

    showReactionPicker(button) {
        const picker = button.parentElement.querySelector('.reaction-picker');
        if (picker) {
            picker.style.opacity = '1';
            picker.style.visibility = 'visible';
            picker.style.transform = 'scale(1)';
            picker.style.pointerEvents = 'auto';

            // Focus first reaction option for keyboard navigation
            const firstOption = picker.querySelector('.reaction-option');
            if (firstOption) {
                firstOption.tabIndex = 0;
            }
        }
    }

    hideReactionPicker(button) {
        const picker = button.parentElement.querySelector('.reaction-picker');
        if (picker) {
            picker.style.opacity = '0';
            picker.style.visibility = 'hidden';
            picker.style.transform = 'scale(0.95)';
            picker.style.pointerEvents = 'none';

            // Remove focus from reaction options
            picker.querySelectorAll('.reaction-option').forEach(option => {
                option.tabIndex = -1;
            });
        }
    }

    async toggleDefaultReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const currentReaction = button.dataset.currentReaction;

        // If user has any reaction, remove it; otherwise add default 'like' reaction
        let reactionType;
        if (currentReaction && currentReaction !== '') {
            // User has a reaction, remove it
            reactionType = null;
        } else {
            // User has no reaction, add 'like'
            reactionType = 'like';
        }

        await this.updateReaction(targetId, targetType, reactionType, button);
    }

    async selectReaction(option) {
        const targetId = option.dataset.targetId;
        const targetType = option.dataset.targetType;
        const reactionType = option.dataset.reactionType;
        const button = option.closest('.relative').querySelector('.reaction-main-btn');
        const currentReaction = button.dataset.currentReaction;
        
        // If clicking the same reaction, toggle it off
        const newReactionType = currentReaction === reactionType ? null : reactionType;
        
        await this.updateReaction(targetId, targetType, newReactionType, button);
        this.hideReactionPicker(button);
    }

    async updateReaction(targetId, targetType, reactionType, button) {
        try {
            const response = await fetch(`/${targetType}s/${targetId}/react`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    reaction_type: reactionType
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateUI(targetId, targetType, data, button);
                this.animateReaction(button, reactionType);
            } else {
                console.error('Reaction update failed:', data.message);
            }
        } catch (error) {
            console.error('Error updating reaction:', error);
        }
    }

    updateUI(targetId, targetType, data, button) {
        const reactions = window.reactionConfig || {};

        // Update button appearance
        if (data.user_reaction) {
            const reaction = reactions[data.user_reaction.reaction_type];
            if (reaction) {
                // Replace SVG with emoji
                const iconElement = button.querySelector('svg') || button.querySelector('span:first-child');
                if (iconElement.tagName === 'svg') {
                    iconElement.outerHTML = `<span class="text-lg" aria-hidden="true">${reaction.emoji}</span>`;
                } else {
                    iconElement.textContent = reaction.emoji;
                }

                // Update text and color
                const textElement = button.querySelector('span:last-child');
                textElement.textContent = reaction.label;

                // Update button color
                button.className = button.className.replace(/text-gray-500|text-blue-600|text-red-600|text-yellow-600|text-purple-600|text-orange-600/, reaction.color);
                button.dataset.currentReaction = data.user_reaction.reaction_type;
            }
        } else {
            // Reset to default - show SVG thumbs up
            const iconElement = button.querySelector('span:first-child') || button.querySelector('svg');
            if (iconElement && iconElement.tagName !== 'svg') {
                iconElement.outerHTML = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M2 15V9a2 2 0 012-2h1.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V15"></path>
                </svg>`;
            }

            // Reset text and color
            const textElement = button.querySelector('span:last-child');
            textElement.textContent = 'Like';

            // Reset button color to gray
            button.className = button.className.replace(/text-blue-600|text-red-600|text-yellow-600|text-purple-600|text-orange-600/, 'text-gray-500');
            button.dataset.currentReaction = '';
        }

        // Update reaction counts if displayed
        this.updateReactionCounts(targetId, targetType, data.reaction_counts);
    }

    updateReactionCounts(targetId, targetType, counts) {
        const countElement = document.querySelector(`#${targetType}-reaction-count-${targetId}`);
        if (countElement && counts) {
            const total = Object.values(counts).reduce((sum, count) => sum + count, 0);
            countElement.textContent = total;
        }
    }

    animateReaction(button, reactionType) {
        if (!reactionType) return;
        
        const reactions = window.reactionConfig || {};
        const reaction = reactions[reactionType];
        
        if (reaction && reaction.animation) {
            const emoji = button.querySelector('span:first-child');
            emoji.classList.add(`reaction-animation-${reaction.animation}`);
            
            setTimeout(() => {
                emoji.classList.remove(`reaction-animation-${reaction.animation}`);
            }, 600);
        }
    }
}

// Initialize reaction system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.reactionSystem = new ReactionSystem();
    
    // Load reaction config from server
    window.reactionConfig = {
        like: { emoji: '👍', label: 'Like', color: 'text-blue-600', animation: 'bounce' },
        love: { emoji: '❤️', label: 'Love', color: 'text-red-600', animation: 'heartBeat' },
        haha: { emoji: '😂', label: 'Haha', color: 'text-yellow-600', animation: 'shake' },
        wow: { emoji: '😮', label: 'Wow', color: 'text-purple-600', animation: 'pulse' },
        sad: { emoji: '😢', label: 'Sad', color: 'text-gray-600', animation: 'fadeIn' },
        angry: { emoji: '😠', label: 'Angry', color: 'text-orange-600', animation: 'shake' }
    };
});

// Legacy support for existing toggleLike function
window.toggleLike = async function(postId) {
    const button = document.querySelector(`#like-btn-${postId}`) || 
                   document.querySelector(`[data-target-id="${postId}"][data-target-type="post"]`);
    
    if (button && window.reactionSystem) {
        await window.reactionSystem.toggleDefaultReaction(button);
    }
};
