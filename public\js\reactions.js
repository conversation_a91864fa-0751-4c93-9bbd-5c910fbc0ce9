/**
 * Facebook-style Reaction System
 * Handles hover/long-press functionality, real-time updates, toggle behavior, and visual feedback
 */

class ReactionSystem {
    constructor() {
        this.longPressTimer = null;
        this.longPressDuration = 500; // milliseconds
        this.isMobile = this.detectMobile();
        this.init();
    }

    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }

    init() {
        this.bindEvents();
        this.setupKeyboardNavigation();
    }

    bindEvents() {
        document.addEventListener('click', this.handleReactionClick.bind(this));
        
        if (this.isMobile) {
            document.addEventListener('touchstart', this.handleTouchStart.bind(this));
            document.addEventListener('touchend', this.handleTouchEnd.bind(this));
        } else {
            document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
            document.addEventListener('mouseleave', this.handleMouseLeave.bind(this), true);
        }
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('reaction-main-btn')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleDefaultReaction(e.target);
                } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    this.showReactionPicker(e.target);
                }
            } else if (e.target.classList.contains('reaction-option')) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.selectReaction(e.target);
                }
            }
        });
    }

    handleMouseEnter(e) {
        if (e.target.classList.contains('reaction-main-btn')) {
            this.showReactionPicker(e.target);
        }
    }

    handleMouseLeave(e) {
        if (e.target.classList.contains('reaction-main-btn')) {
            setTimeout(() => {
                const picker = e.target.parentElement.querySelector('.reaction-picker');
                if (picker && !picker.matches(':hover')) {
                    this.hideReactionPicker(e.target);
                }
            }, 100);
        }
    }

    handleTouchStart(e) {
        if (e.target.classList.contains('reaction-main-btn')) {
            this.longPressTimer = setTimeout(() => {
                this.showReactionPicker(e.target);
                // Add haptic feedback if available
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }, this.longPressDuration);
        }
    }

    handleTouchEnd(e) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    handleReactionClick(e) {
        if (e.target.classList.contains('reaction-option')) {
            e.preventDefault();
            e.stopPropagation();
            this.selectReaction(e.target);
        } else if (e.target.classList.contains('reaction-main-btn') || 
                   e.target.closest('.reaction-main-btn')) {
            const btn = e.target.classList.contains('reaction-main-btn') ? 
                       e.target : e.target.closest('.reaction-main-btn');
            
            if (!this.isMobile) {
                this.toggleDefaultReaction(btn);
            }
        }
    }

    showReactionPicker(button) {
        const picker = button.parentElement.querySelector('.reaction-picker');
        if (picker) {
            picker.style.opacity = '1';
            picker.style.visibility = 'visible';
            picker.style.transform = 'translateX(-50%) scale(1)';
            
            // Focus first reaction option for keyboard navigation
            const firstOption = picker.querySelector('.reaction-option');
            if (firstOption) {
                firstOption.tabIndex = 0;
            }
        }
    }

    hideReactionPicker(button) {
        const picker = button.parentElement.querySelector('.reaction-picker');
        if (picker) {
            picker.style.opacity = '0';
            picker.style.visibility = 'hidden';
            picker.style.transform = 'translateX(-50%) scale(0.95)';
            
            // Remove focus from reaction options
            picker.querySelectorAll('.reaction-option').forEach(option => {
                option.tabIndex = -1;
            });
        }
    }

    async toggleDefaultReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const currentReaction = button.dataset.currentReaction;
        
        // If user has a reaction, remove it; otherwise add default reaction
        const reactionType = currentReaction ? null : 'like';
        
        await this.updateReaction(targetId, targetType, reactionType, button);
    }

    async selectReaction(option) {
        const targetId = option.dataset.targetId;
        const targetType = option.dataset.targetType;
        const reactionType = option.dataset.reactionType;
        const button = option.closest('.relative').querySelector('.reaction-main-btn');
        const currentReaction = button.dataset.currentReaction;
        
        // If clicking the same reaction, toggle it off
        const newReactionType = currentReaction === reactionType ? null : reactionType;
        
        await this.updateReaction(targetId, targetType, newReactionType, button);
        this.hideReactionPicker(button);
    }

    async updateReaction(targetId, targetType, reactionType, button) {
        try {
            const response = await fetch(`/${targetType}s/${targetId}/react`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    reaction_type: reactionType
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateUI(targetId, targetType, data, button);
                this.animateReaction(button, reactionType);
            } else {
                console.error('Reaction update failed:', data.message);
            }
        } catch (error) {
            console.error('Error updating reaction:', error);
        }
    }

    updateUI(targetId, targetType, data, button) {
        const reactions = window.reactionConfig || {};
        
        // Update button appearance
        if (data.user_reaction) {
            const reaction = reactions[data.user_reaction.reaction_type];
            if (reaction) {
                button.querySelector('span:first-child').textContent = reaction.emoji;
                button.querySelector('span:last-child').textContent = reaction.label;
                button.querySelector('span:last-child').className = `text-sm font-medium ${reaction.color}`;
                button.dataset.currentReaction = data.user_reaction.reaction_type;
            }
        } else {
            // Reset to default
            button.querySelector('span:first-child').textContent = '👍';
            button.querySelector('span:last-child').textContent = 'Like';
            button.querySelector('span:last-child').className = 'text-sm font-medium';
            button.dataset.currentReaction = '';
        }

        // Update reaction counts if displayed
        this.updateReactionCounts(targetId, targetType, data.reaction_counts);
    }

    updateReactionCounts(targetId, targetType, counts) {
        const countElement = document.querySelector(`#${targetType}-reaction-count-${targetId}`);
        if (countElement && counts) {
            const total = Object.values(counts).reduce((sum, count) => sum + count, 0);
            countElement.textContent = total;
        }
    }

    animateReaction(button, reactionType) {
        if (!reactionType) return;
        
        const reactions = window.reactionConfig || {};
        const reaction = reactions[reactionType];
        
        if (reaction && reaction.animation) {
            const emoji = button.querySelector('span:first-child');
            emoji.classList.add(`reaction-animation-${reaction.animation}`);
            
            setTimeout(() => {
                emoji.classList.remove(`reaction-animation-${reaction.animation}`);
            }, 600);
        }
    }
}

// Initialize reaction system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.reactionSystem = new ReactionSystem();
    
    // Load reaction config from server
    window.reactionConfig = {
        like: { emoji: '👍', label: 'Like', color: 'text-blue-600', animation: 'bounce' },
        love: { emoji: '❤️', label: 'Love', color: 'text-red-600', animation: 'heartBeat' },
        haha: { emoji: '😂', label: 'Haha', color: 'text-yellow-600', animation: 'shake' },
        wow: { emoji: '😮', label: 'Wow', color: 'text-purple-600', animation: 'pulse' },
        sad: { emoji: '😢', label: 'Sad', color: 'text-gray-600', animation: 'fadeIn' },
        angry: { emoji: '😠', label: 'Angry', color: 'text-orange-600', animation: 'shake' }
    };
});

// Legacy support for existing toggleLike function
window.toggleLike = async function(postId) {
    const button = document.querySelector(`#like-btn-${postId}`) || 
                   document.querySelector(`[data-target-id="${postId}"][data-target-type="post"]`);
    
    if (button && window.reactionSystem) {
        await window.reactionSystem.toggleDefaultReaction(button);
    }
};
