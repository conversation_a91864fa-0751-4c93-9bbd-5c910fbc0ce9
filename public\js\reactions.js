/**
 * Simplified Facebook-style Reaction System
 */

class ReactionSystem {
    constructor() {
        console.log('ReactionSystem initialized');
        this.reactions = {
            like: { emoji: '👍', label: 'Like', color: 'text-blue-600' },
            love: { emoji: '❤️', label: 'Love', color: 'text-red-600' },
            haha: { emoji: '😂', label: 'Haha', color: 'text-yellow-600' },
            wow: { emoji: '😮', label: 'Wow', color: 'text-purple-600' },
            sad: { emoji: '😢', label: 'Sad', color: 'text-gray-600' },
            angry: { emoji: '😠', label: 'Angry', color: 'text-orange-600' }
        };
    }

    async toggleDefaultReaction(button) {
        console.log('toggleDefaultReaction called', button);
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const currentReaction = button.dataset.currentReaction;

        console.log('Current reaction:', currentReaction);

        // If user has any reaction, remove it; otherwise add default 'like' reaction
        let reactionType;
        if (currentReaction && currentReaction !== '' && currentReaction !== 'null') {
            reactionType = null; // Remove reaction
            console.log('Removing reaction');
        } else {
            reactionType = 'like'; // Add like reaction
            console.log('Adding like reaction');
        }

        await this.updateReaction(targetId, targetType, reactionType, button);
    }

    async selectReaction(option) {
        console.log('selectReaction called', option);
        const targetId = option.dataset.targetId;
        const targetType = option.dataset.targetType;
        const reactionType = option.dataset.reactionType;
        const button = option.closest('.reaction-wrapper').querySelector('.reaction-btn');
        const currentReaction = button.dataset.currentReaction;

        console.log('Selecting reaction:', reactionType, 'Current:', currentReaction);

        // If clicking the same reaction, toggle it off
        const newReactionType = currentReaction === reactionType ? null : reactionType;

        await this.updateReaction(targetId, targetType, newReactionType, button);
    }

    async updateReaction(targetId, targetType, reactionType, button) {
        console.log('updateReaction called:', targetId, targetType, reactionType);

        try {
            const response = await fetch(`/${targetType}s/${targetId}/react`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    reaction_type: reactionType
                })
            });

            console.log('Response status:', response.status);
            const data = await response.json();
            console.log('Response data:', data);

            if (data.success) {
                this.updateUI(targetId, targetType, data, button);
            } else {
                console.error('Reaction update failed:', data.message);
                alert('Error: ' + data.message);
            }
        } catch (error) {
            console.error('Error updating reaction:', error);
            alert('Network error: ' + error.message);
        }
    }

    updateUI(targetId, targetType, data, button) {
        console.log('updateUI called:', data);

        // Update button appearance
        if (data.user_reaction) {
            const reaction = this.reactions[data.user_reaction.reaction_type];
            if (reaction) {
                // Update icon
                const iconElement = button.querySelector('svg') || button.querySelector('span:first-child');
                if (iconElement.tagName === 'svg') {
                    iconElement.outerHTML = `<span class="text-lg">${reaction.emoji}</span>`;
                } else {
                    iconElement.textContent = reaction.emoji;
                }

                // Update text
                const textElement = button.querySelector('span:last-child');
                textElement.textContent = reaction.label;

                // Update button color
                button.className = button.className.replace(/text-gray-500|text-blue-600|text-red-600|text-yellow-600|text-purple-600|text-orange-600/, reaction.color);
                button.dataset.currentReaction = data.user_reaction.reaction_type;
            }
        } else {
            // Reset to default
            const iconElement = button.querySelector('span:first-child') || button.querySelector('svg');
            if (iconElement && iconElement.tagName !== 'svg') {
                iconElement.outerHTML = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M2 15V9a2 2 0 012-2h1.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V15"></path>
                </svg>`;
            }

            const textElement = button.querySelector('span:last-child');
            textElement.textContent = 'Like';

            button.className = button.className.replace(/text-blue-600|text-red-600|text-yellow-600|text-purple-600|text-orange-600/, 'text-gray-500');
            button.dataset.currentReaction = '';
        }
    }
}

// Initialize reaction system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing reaction system...');
    window.reactionSystem = new ReactionSystem();

    // Test elements
    const wrappers = document.querySelectorAll('.reaction-wrapper');
    const buttons = document.querySelectorAll('.reaction-btn');
    const options = document.querySelectorAll('.reaction-btn-option');

    console.log('Found elements:', {
        wrappers: wrappers.length,
        buttons: buttons.length,
        options: options.length
    });
});

// Legacy support
window.toggleLike = async function(postId) {
    const button = document.querySelector(`[data-target-id="${postId}"][data-target-type="post"]`);
    if (button && window.reactionSystem) {
        await window.reactionSystem.toggleDefaultReaction(button);
    }
};
